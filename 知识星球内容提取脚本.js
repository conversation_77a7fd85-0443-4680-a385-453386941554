// 知识星球内容自动提取脚本
// 使用方法：在知识星球页面按F12打开开发者工具，在Console中粘贴并运行此脚本

(function() {
    console.log('开始提取知识星球内容...');
    
    let extractedContent = {
        title: '',
        chapters: [],
        images: []
    };
    
    // 提取页面标题
    function getTitle() {
        const titleSelectors = [
            'h1',
            '.title',
            '[class*="title"]',
            '[class*="header"]'
        ];
        
        for (let selector of titleSelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
                return element.textContent.trim();
            }
        }
        return '知识星球教程';
    }
    
    // 提取所有文本内容
    function extractAllText() {
        const contentSelectors = [
            '[class*="content"]',
            '[class*="article"]',
            '[class*="post"]',
            '[class*="detail"]',
            'main',
            '.main'
        ];
        
        let contentArea = null;
        for (let selector of contentSelectors) {
            contentArea = document.querySelector(selector);
            if (contentArea) break;
        }
        
        if (!contentArea) {
            contentArea = document.body;
        }
        
        return contentArea.innerText;
    }
    
    // 提取图片信息
    function extractImages() {
        const images = document.querySelectorAll('img');
        const imageInfo = [];
        
        images.forEach((img, index) => {
            if (img.src && !img.src.includes('data:image') && img.width > 50 && img.height > 50) {
                imageInfo.push({
                    index: index + 1,
                    src: img.src,
                    alt: img.alt || `图片${index + 1}`,
                    width: img.width,
                    height: img.height
                });
            }
        });
        
        return imageInfo;
    }
    
    // 尝试提取章节结构
    function extractChapters() {
        const chapters = [];
        
        // 查找可能的章节标题
        const headingSelectors = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
        const headings = [];
        
        headingSelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(heading => {
                if (heading.textContent.trim()) {
                    headings.push({
                        level: parseInt(selector.charAt(1)),
                        text: heading.textContent.trim(),
                        element: heading
                    });
                }
            });
        });
        
        // 如果没有找到标准标题，尝试查找其他可能的章节标识
        if (headings.length === 0) {
            const possibleChapterSelectors = [
                '[class*="chapter"]',
                '[class*="section"]',
                '[class*="lesson"]',
                '[class*="item"]'
            ];
            
            possibleChapterSelectors.forEach(selector => {
                document.querySelectorAll(selector).forEach(element => {
                    if (element.textContent.trim()) {
                        chapters.push({
                            title: element.textContent.trim().substring(0, 100),
                            content: element.textContent.trim()
                        });
                    }
                });
            });
        } else {
            // 使用找到的标题构建章节
            headings.forEach(heading => {
                chapters.push({
                    title: heading.text,
                    level: heading.level,
                    content: ''
                });
            });
        }
        
        return chapters;
    }
    
    // 执行提取
    extractedContent.title = getTitle();
    extractedContent.chapters = extractChapters();
    extractedContent.images = extractImages();
    extractedContent.fullText = extractAllText();
    
    // 生成Markdown格式的内容
    function generateMarkdown() {
        let markdown = `# ${extractedContent.title}\n\n`;
        
        // 添加目录
        if (extractedContent.chapters.length > 0) {
            markdown += '## 目录\n\n';
            extractedContent.chapters.forEach((chapter, index) => {
                const indent = '  '.repeat((chapter.level || 1) - 1);
                markdown += `${indent}- ${chapter.title}\n`;
            });
            markdown += '\n---\n\n';
        }
        
        // 添加章节内容
        if (extractedContent.chapters.length > 0) {
            extractedContent.chapters.forEach((chapter, index) => {
                const level = '#'.repeat(chapter.level || 2);
                markdown += `${level} ${chapter.title}\n\n`;
                if (chapter.content) {
                    markdown += `${chapter.content}\n\n`;
                }
            });
        } else {
            // 如果没有章节结构，直接添加全文
            markdown += '## 内容\n\n';
            markdown += extractedContent.fullText + '\n\n';
        }
        
        // 添加图片信息
        if (extractedContent.images.length > 0) {
            markdown += '## 图片列表\n\n';
            extractedContent.images.forEach(img => {
                markdown += `### ${img.alt}\n`;
                markdown += `- 链接: ${img.src}\n`;
                markdown += `- 尺寸: ${img.width}x${img.height}\n\n`;
            });
        }
        
        return markdown;
    }
    
    const markdownContent = generateMarkdown();
    
    // 输出结果
    console.log('=== 提取完成 ===');
    console.log('标题:', extractedContent.title);
    console.log('章节数量:', extractedContent.chapters.length);
    console.log('图片数量:', extractedContent.images.length);
    console.log('\n=== Markdown内容 ===');
    console.log(markdownContent);
    
    // 创建下载链接
    const blob = new Blob([markdownContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${extractedContent.title}.md`;
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.log('文件已自动下载！');
    
    // 返回结果供进一步处理
    return {
        content: extractedContent,
        markdown: markdownContent
    };
})();
