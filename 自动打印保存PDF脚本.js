// 知识星球自动打印保存PDF脚本
// 使用方法：在知识星球页面按F12打开开发者工具，在Console中粘贴并运行此脚本

(function() {
    console.log('开始自动打印保存PDF...');
    
    // 优化页面样式以便更好地打印
    function optimizeForPrint() {
        // 创建打印样式
        const printStyle = document.createElement('style');
        printStyle.id = 'auto-print-style';
        printStyle.innerHTML = `
            @media print {
                /* 隐藏不必要的元素 */
                nav, header, footer, .sidebar, .menu, .navigation,
                [class*="nav"], [class*="menu"], [class*="sidebar"],
                [class*="header"], [class*="footer"], [class*="ad"],
                .advertisement, .ads, button, .btn {
                    display: none !important;
                }
                
                /* 确保内容区域占满页面 */
                body {
                    margin: 0 !important;
                    padding: 20px !important;
                    font-size: 12pt !important;
                    line-height: 1.4 !important;
                    color: black !important;
                    background: white !important;
                }
                
                /* 优化文本显示 */
                h1, h2, h3, h4, h5, h6 {
                    color: black !important;
                    page-break-after: avoid !important;
                    margin-top: 20px !important;
                    margin-bottom: 10px !important;
                }
                
                /* 确保图片正确显示 */
                img {
                    max-width: 100% !important;
                    height: auto !important;
                    page-break-inside: avoid !important;
                    display: block !important;
                    margin: 10px 0 !important;
                }
                
                /* 避免内容被截断 */
                .content, .article, .post, .detail, main {
                    page-break-inside: avoid !important;
                }
                
                /* 确保背景图片和颜色显示 */
                * {
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
                
                /* 分页控制 */
                .chapter, .section, .lesson {
                    page-break-before: auto !important;
                    page-break-after: avoid !important;
                }
                
                /* 链接样式 */
                a {
                    color: black !important;
                    text-decoration: underline !important;
                }
                
                /* 表格样式 */
                table {
                    border-collapse: collapse !important;
                    width: 100% !important;
                }
                
                table, th, td {
                    border: 1px solid black !important;
                    padding: 5px !important;
                }
            }
        `;
        
        document.head.appendChild(printStyle);
        console.log('✓ 页面样式已优化');
    }
    
    // 等待图片加载完成
    function waitForImages() {
        return new Promise((resolve) => {
            const images = document.querySelectorAll('img');
            let loadedCount = 0;
            const totalImages = images.length;
            
            if (totalImages === 0) {
                resolve();
                return;
            }
            
            console.log(`等待 ${totalImages} 张图片加载...`);
            
            images.forEach((img) => {
                if (img.complete) {
                    loadedCount++;
                } else {
                    img.onload = img.onerror = () => {
                        loadedCount++;
                        if (loadedCount === totalImages) {
                            resolve();
                        }
                    };
                }
            });
            
            if (loadedCount === totalImages) {
                resolve();
            }
            
            // 最多等待10秒
            setTimeout(resolve, 10000);
        });
    }
    
    // 滚动页面确保所有内容加载
    function scrollToLoadContent() {
        return new Promise((resolve) => {
            console.log('滚动页面加载所有内容...');
            
            let scrollPosition = 0;
            const scrollStep = 500;
            const scrollDelay = 200;
            
            function scroll() {
                window.scrollTo(0, scrollPosition);
                scrollPosition += scrollStep;
                
                if (scrollPosition < document.body.scrollHeight) {
                    setTimeout(scroll, scrollDelay);
                } else {
                    // 滚动到顶部
                    window.scrollTo(0, 0);
                    setTimeout(resolve, 500);
                }
            }
            
            scroll();
        });
    }
    
    // 自动触发打印对话框
    function triggerPrint() {
        console.log('触发打印对话框...');
        
        // 设置打印参数（如果浏览器支持）
        if (window.chrome && window.chrome.runtime) {
            // Chrome浏览器的特殊处理
            console.log('检测到Chrome浏览器');
        }
        
        // 触发打印
        window.print();
        
        console.log('✓ 打印对话框已打开');
        console.log('请在打印对话框中：');
        console.log('1. 选择"保存为PDF"');
        console.log('2. 点击"更多设置"');
        console.log('3. 勾选"背景图形"');
        console.log('4. 点击"保存"');
    }
    
    // 创建提示信息
    function showInstructions() {
        const instructionDiv = document.createElement('div');
        instructionDiv.id = 'print-instructions';
        instructionDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 15px;
            border-radius: 5px;
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 14px;
            max-width: 300px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        `;
        
        instructionDiv.innerHTML = `
            <strong>📄 自动打印提示</strong><br>
            打印对话框打开后请：<br>
            1️⃣ 选择"保存为PDF"<br>
            2️⃣ 点击"更多设置"<br>
            3️⃣ 勾选"背景图形"<br>
            4️⃣ 点击"保存"<br>
            <button onclick="this.parentElement.remove()" style="margin-top:10px;padding:5px;background:white;border:none;border-radius:3px;cursor:pointer;">关闭</button>
        `;
        
        document.body.appendChild(instructionDiv);
        
        // 5秒后自动移除提示
        setTimeout(() => {
            if (instructionDiv.parentElement) {
                instructionDiv.remove();
            }
        }, 15000);
    }
    
    // 主执行函数
    async function executePrintSave() {
        try {
            console.log('=== 开始自动打印保存流程 ===');
            
            // 1. 优化页面样式
            optimizeForPrint();
            
            // 2. 滚动加载内容
            await scrollToLoadContent();
            
            // 3. 等待图片加载
            await waitForImages();
            console.log('✓ 所有图片已加载完成');
            
            // 4. 显示操作提示
            showInstructions();
            
            // 5. 等待一下让页面稳定
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 6. 触发打印
            triggerPrint();
            
            console.log('=== 自动打印流程完成 ===');
            console.log('请按照提示完成PDF保存操作');
            
        } catch (error) {
            console.error('执行过程中出现错误:', error);
            alert('自动打印过程中出现错误，请手动按Ctrl+P进行打印');
        }
    }
    
    // 执行主函数
    executePrintSave();
    
    // 返回清理函数
    return {
        cleanup: function() {
            const style = document.getElementById('auto-print-style');
            const instructions = document.getElementById('print-instructions');
            if (style) style.remove();
            if (instructions) instructions.remove();
            console.log('已清理临时元素');
        }
    };
})();
