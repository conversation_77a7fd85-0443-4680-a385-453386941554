// 内容脚本 - 在知识星球页面自动运行

// 添加一键保存按钮到页面
function addSaveButton() {
    const saveBtn = document.createElement('button');
    saveBtn.innerHTML = '🚀 一键保存PDF';
    saveBtn.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        background: #4CAF50;
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    `;
    
    saveBtn.addEventListener('click', () => {
        autoSavePDF();
    });
    
    document.body.appendChild(saveBtn);
}

function autoSavePDF() {
    // 优化页面样式
    const style = document.createElement('style');
    style.innerHTML = `
        @media print {
            nav, header, footer, .sidebar, .menu, .advertisement, .ads, button { display: none !important; }
            body { margin: 0 !important; padding: 20px !important; background: white !important; }
            img { max-width: 100% !important; height: auto !important; }
            * { -webkit-print-color-adjust: exact !important; color-adjust: exact !important; }
        }
    `;
    document.head.appendChild(style);
    
    // 滚动加载所有内容
    let scrollPosition = 0;
    const scrollStep = 500;
    
    function scroll() {
        window.scrollTo(0, scrollPosition);
        scrollPosition += scrollStep;
        
        if (scrollPosition < document.body.scrollHeight) {
            setTimeout(scroll, 100);
        } else {
            // 滚动完成，触发打印
            window.scrollTo(0, 0);
            setTimeout(() => {
                window.print();
            }, 1000);
        }
    }
    
    scroll();
}

// 页面加载完成后添加按钮
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addSaveButton);
} else {
    addSaveButton();
}
