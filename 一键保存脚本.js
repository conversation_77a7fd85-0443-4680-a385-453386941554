// 一键保存知识星球内容脚本
// 复制到Console运行，然后只需要按一次Ctrl+S保存

(function() {
    console.log('🚀 开始一键保存流程...');
    
    // 自动优化并触发打印
    function autoSave() {
        // 1. 优化页面样式
        const style = document.createElement('style');
        style.innerHTML = `
            @media print {
                nav, header, footer, .sidebar, .menu, .advertisement, .ads, button { display: none !important; }
                body { margin: 0 !important; padding: 20px !important; background: white !important; }
                img { max-width: 100% !important; height: auto !important; }
                * { -webkit-print-color-adjust: exact !important; color-adjust: exact !important; }
            }
        `;
        document.head.appendChild(style);
        
        // 2. 等待1秒让页面稳定
        setTimeout(() => {
            // 3. 自动触发打印
            window.print();
            
            // 4. 显示简单提示
            alert('打印对话框已打开！\n\n请选择：\n1. 保存为PDF\n2. 勾选"背景图形"\n3. 点击保存');
        }, 1000);
    }
    
    autoSave();
})();

// 运行后只需要在打印对话框中点击"保存为PDF"即可
