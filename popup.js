document.getElementById('saveBtn').addEventListener('click', async () => {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    chrome.scripting.executeScript({
        target: { tabId: tab.id },
        function: autoSavePDF
    });
    
    window.close();
});

function autoSavePDF() {
    // 优化页面样式
    const style = document.createElement('style');
    style.innerHTML = `
        @media print {
            nav, header, footer, .sidebar, .menu, .advertisement, .ads, button { display: none !important; }
            body { margin: 0 !important; padding: 20px !important; background: white !important; }
            img { max-width: 100% !important; height: auto !important; }
            * { -webkit-print-color-adjust: exact !important; color-adjust: exact !important; }
        }
    `;
    document.head.appendChild(style);
    
    // 自动触发打印
    setTimeout(() => {
        window.print();
    }, 500);
}
